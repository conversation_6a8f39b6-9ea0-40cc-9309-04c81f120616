# Plan Implementasi Idempotency untuk ATMA Backend

## Overview
Implementasi idempotency untuk memastikan bahwa request yang sama yang dikirim berulang kali tidak menghasilkan efek duplikasi di sistem ATMA.

## Analisis Situasi Saat Ini

### Assessment Service
- ❌ **Tidak ada mekanisme idempotency**
- ❌ Setiap request generate jobId baru tanpa cek duplikasi
- ❌ Tidak ada validasi assessment data yang sama

### Analysis Worker  
- ✅ **Sudah ada JobDeduplicationService**
- ✅ Hash-based duplicate detection
- ✅ Token refund mechanism
- ⚠️ Hanya bekerja di level worker, tidak di submission

### Archive Service
- ❌ **Tidak ada unique constraint berdasarkan content**
- ❌ Bisa simpan hasil analisis duplikat
- ❌ Tidak ada mekanisme deteksi hasil yang sama

### Notification Service
- ❌ **Tidak ada duplicate notification prevention**
- ❌ Setiap call endpoint kirim notifikasi tanpa cek

## Strategi Implementasi

### 1. Assessment Service - Request Deduplication

**Tujuan**: Mencegah submission assessment data yang sama dalam periode tertentu

**File yang Terpengaruh**:
- `assessment-service/src/routes/assessments.js`
- `assessment-service/src/services/deduplicationService.js` (baru)
- `assessment-service/src/middleware/idempotencyMiddleware.js` (baru)

**Implementasi**:
1. **Buat Assessment Hash Service**
   - Generate hash dari `userId + assessmentData + assessmentName`
   - Simpan hash dengan TTL di Redis/memory cache
   - Include timestamp untuk tracking

2. **Idempotency Middleware**
   - Check hash sebelum processing
   - Return existing jobId jika duplikat dalam window time
   - Support idempotency key dari client (optional)

3. **Modifikasi Submit Endpoint**
   - Tambah idempotency check sebelum create job
   - Return existing job status jika duplikat
   - Prevent token deduction untuk duplicate

**Yang Harus Diperhatikan**:
- Window time untuk duplikasi (default: 1 jam)
- Handling concurrent requests dengan lock mechanism
- Cleanup expired entries
- Backward compatibility dengan existing clients

### 2. Archive Service - Result Deduplication

**Tujuan**: Mencegah penyimpanan hasil analisis yang identik

**File yang Terpengaruh**:
- `archive-service/src/models/AnalysisResult.js`
- `archive-service/src/services/resultsService.js`
- `archive-service/src/controllers/resultsController.js`
- `archive-service/migrations/add-content-hash-index.js` (baru)

**Implementasi**:
1. **Database Schema Changes**
   - Tambah kolom `content_hash` di table `analysis_results`
   - Create unique index: `(user_id, content_hash, assessment_name)`
   - Add index untuk performance: `(content_hash)`

2. **Content Hash Generation**
   - Hash dari `userId + assessmentData + assessmentName`
   - Consistent dengan hash di assessment service
   - Store hash saat create result

3. **Duplicate Detection Logic**
   - Check existing result dengan hash yang sama
   - Return existing result ID jika duplikat
   - Update timestamp untuk existing result (optional)

4. **Batch Processing Update**
   - Modify batch processor untuk handle duplicates
   - Skip duplicate entries dalam batch
   - Return existing result untuk duplicates

**Yang Harus Diperhatikan**:
- Migration strategy untuk existing data
- Performance impact dari unique constraint
- Handling hash collision (sangat jarang)
- Backward compatibility dengan existing results

### 3. Notification Service - Duplicate Prevention

**Tujuan**: Mencegah pengiriman notifikasi duplikat ke client

**File yang Terpengaruh**:
- `notification-service/src/routes/notifications.js`
- `notification-service/src/services/notificationDeduplicationService.js` (baru)
- `notification-service/src/services/socketService.js`
- `notification-service/src/services/eventConsumer.js`

**Implementasi**:
1. **Notification Tracking Service**
   - Track sent notifications dengan key: `userId:jobId:eventType`
   - TTL-based cleanup (default: 24 jam)
   - Store notification metadata

2. **Deduplication Middleware**
   - Check sebelum send notification
   - Skip jika sudah pernah dikirim
   - Log duplicate attempts

3. **Socket Service Enhancement**
   - Add deduplication check di `sendToUser`
   - Track message IDs untuk WebSocket
   - Prevent duplicate real-time notifications

4. **Event Consumer Update**
   - Add deduplication untuk event-based notifications
   - Consistent dengan HTTP endpoint behavior

**Yang Harus Diperhatikan**:
- Memory usage untuk tracking
- TTL configuration yang optimal
- Handling reconnection scenarios
- Message ordering untuk WebSocket

## Implementation Phases

### Phase 1: Assessment Service (Priority: HIGH)
**Timeline**: 2-3 hari
- Implement deduplication service
- Add idempotency middleware
- Update submit endpoint
- Testing dengan existing flow

### Phase 2: Archive Service (Priority: HIGH)  
**Timeline**: 2-3 hari
- Database migration untuk content_hash
- Update result creation logic
- Modify batch processing
- Data migration untuk existing records

### Phase 3: Notification Service (Priority: MEDIUM)
**Timeline**: 1-2 hari
- Implement notification tracking
- Update endpoints dan event consumer
- Socket service enhancement
- Integration testing

### Phase 4: Integration & Testing (Priority: HIGH)
**Timeline**: 1-2 hari
- End-to-end testing
- Performance testing
- Load testing untuk concurrent requests
- Documentation update

## Configuration & Monitoring

### Environment Variables
```env
# Assessment Service
IDEMPOTENCY_WINDOW_MS=3600000  # 1 hour
IDEMPOTENCY_CACHE_SIZE=10000
ENABLE_IDEMPOTENCY=true

# Archive Service  
ENABLE_CONTENT_HASH=true
CONTENT_HASH_ALGORITHM=sha256

# Notification Service
NOTIFICATION_TTL_MS=86400000  # 24 hours
ENABLE_NOTIFICATION_DEDUP=true
```

### Monitoring Metrics
- Duplicate request rate per service
- Cache hit/miss ratios
- Performance impact measurements
- Error rates untuk idempotency logic

## Rollback Strategy
1. Feature flags untuk enable/disable idempotency
2. Database migration rollback scripts
3. Graceful degradation jika cache unavailable
4. Monitoring untuk detect issues

## Testing Strategy
1. Unit tests untuk deduplication logic
2. Integration tests untuk cross-service flow
3. Load tests untuk concurrent duplicate requests
4. Edge case testing (hash collisions, cache failures)

## Success Criteria
- ✅ Duplicate submissions tidak create multiple jobs
- ✅ Duplicate analysis results tidak tersimpan
- ✅ Duplicate notifications tidak terkirim
- ✅ Performance impact < 5% untuk normal requests
- ✅ Zero data loss atau corruption

## Detailed Implementation Specifications

### Assessment Service Implementation

#### 1. Deduplication Service (`assessment-service/src/services/deduplicationService.js`)

```javascript
class AssessmentDeduplicationService {
  constructor() {
    this.cache = new Map(); // In production: use Redis
    this.windowMs = process.env.IDEMPOTENCY_WINDOW_MS || 3600000; // 1 hour
  }

  generateAssessmentHash(userId, assessmentData, assessmentName) {
    const content = {
      userId,
      assessmentName,
      riasec: assessmentData.riasec,
      ocean: assessmentData.ocean,
      viaIs: assessmentData.viaIs
    };
    return crypto.createHash('sha256')
      .update(JSON.stringify(content))
      .digest('hex');
  }

  async checkDuplicate(userId, assessmentData, assessmentName) {
    const hash = this.generateAssessmentHash(userId, assessmentData, assessmentName);
    const existing = this.cache.get(hash);

    if (existing && (Date.now() - existing.timestamp) < this.windowMs) {
      return {
        isDuplicate: true,
        existingJobId: existing.jobId,
        originalTimestamp: existing.timestamp
      };
    }

    return { isDuplicate: false, hash };
  }

  async recordSubmission(hash, jobId, userId) {
    this.cache.set(hash, {
      jobId,
      userId,
      timestamp: Date.now()
    });
  }
}
```

#### 2. Idempotency Middleware (`assessment-service/src/middleware/idempotencyMiddleware.js`)

```javascript
const idempotencyCheck = async (req, res, next) => {
  try {
    const { id: userId } = req.user;
    const { assessmentName, ...assessmentData } = req.body;

    const result = await deduplicationService.checkDuplicate(
      userId,
      assessmentData,
      assessmentName || 'AI-Driven Talent Mapping'
    );

    if (result.isDuplicate) {
      // Get existing job status
      const jobStatus = await jobTracker.getJobStatus(result.existingJobId);

      return sendSuccess(res, 'Assessment already submitted', {
        jobId: result.existingJobId,
        status: jobStatus.status,
        isDuplicate: true,
        originalTimestamp: result.originalTimestamp
      }, 200);
    }

    // Store hash for later use
    req.assessmentHash = result.hash;
    next();
  } catch (error) {
    next(error);
  }
};
```

### Archive Service Implementation

#### 1. Database Migration (`archive-service/migrations/add-content-hash.js`)

```sql
-- Add content_hash column
ALTER TABLE archive.analysis_results
ADD COLUMN content_hash VARCHAR(64);

-- Create index for performance
CREATE INDEX idx_analysis_results_content_hash
ON archive.analysis_results(content_hash);

-- Create unique constraint for deduplication
CREATE UNIQUE INDEX idx_analysis_results_unique_content
ON archive.analysis_results(user_id, content_hash, assessment_name)
WHERE status = 'completed';
```

#### 2. Enhanced Results Service (`archive-service/src/services/resultsService.js`)

```javascript
// Add to existing resultsService
const generateContentHash = (userId, assessmentData, assessmentName) => {
  const content = {
    userId,
    assessmentName,
    riasec: assessmentData.riasec,
    ocean: assessmentData.ocean,
    viaIs: assessmentData.viaIs
  };
  return crypto.createHash('sha256')
    .update(JSON.stringify(content))
    .digest('hex');
};

const createResultWithDeduplication = async (data) => {
  const contentHash = generateContentHash(
    data.user_id,
    data.assessment_data,
    data.assessment_name
  );

  // Check for existing result
  const existingResult = await AnalysisResult.findOne({
    where: {
      user_id: data.user_id,
      content_hash: contentHash,
      assessment_name: data.assessment_name,
      status: 'completed'
    }
  });

  if (existingResult) {
    logger.info('Duplicate result detected, returning existing', {
      existingId: existingResult.id,
      userId: data.user_id,
      contentHash
    });

    return existingResult;
  }

  // Create new result with hash
  const resultData = {
    ...data,
    content_hash: contentHash
  };

  return await AnalysisResult.create(resultData);
};
```

### Notification Service Implementation

#### 1. Notification Deduplication Service (`notification-service/src/services/notificationDeduplicationService.js`)

```javascript
class NotificationDeduplicationService {
  constructor() {
    this.sentNotifications = new Map();
    this.ttlMs = process.env.NOTIFICATION_TTL_MS || 86400000; // 24 hours
  }

  generateNotificationKey(userId, jobId, eventType) {
    return `${userId}:${jobId}:${eventType}`;
  }

  async checkDuplicate(userId, jobId, eventType) {
    const key = this.generateNotificationKey(userId, jobId, eventType);
    const existing = this.sentNotifications.get(key);

    if (existing && (Date.now() - existing.timestamp) < this.ttlMs) {
      return {
        isDuplicate: true,
        originalTimestamp: existing.timestamp
      };
    }

    return { isDuplicate: false, key };
  }

  async recordNotification(key, userId, jobId, eventType) {
    this.sentNotifications.set(key, {
      userId,
      jobId,
      eventType,
      timestamp: Date.now()
    });
  }

  // Cleanup expired entries
  cleanup() {
    const now = Date.now();
    for (const [key, value] of this.sentNotifications.entries()) {
      if (now - value.timestamp > this.ttlMs) {
        this.sentNotifications.delete(key);
      }
    }
  }
}
```

#### 2. Enhanced Socket Service (`notification-service/src/services/socketService.js`)

```javascript
// Add to existing socketService
const sendToUserWithDeduplication = async (userId, event, data) => {
  const { jobId } = data;

  // Check for duplicate
  const dupCheck = await notificationDeduplicationService.checkDuplicate(
    userId,
    jobId,
    event
  );

  if (dupCheck.isDuplicate) {
    logger.info('Duplicate notification prevented', {
      userId,
      jobId,
      event,
      originalTimestamp: dupCheck.originalTimestamp
    });
    return false;
  }

  // Send notification
  const sent = this.sendToUser(userId, event, data);

  if (sent) {
    // Record successful notification
    await notificationDeduplicationService.recordNotification(
      dupCheck.key,
      userId,
      jobId,
      event
    );
  }

  return sent;
};
```

## Risk Mitigation

### 1. Hash Collision Risk
- **Probability**: Extremely low dengan SHA-256
- **Mitigation**: Include timestamp dalam hash jika diperlukan
- **Monitoring**: Log semua hash collisions

### 2. Cache Memory Usage
- **Risk**: Memory leak dari cache yang tidak dibersihkan
- **Mitigation**: TTL-based cleanup + size limits
- **Monitoring**: Memory usage metrics

### 3. Performance Impact
- **Risk**: Latency increase dari hash computation
- **Mitigation**: Async processing + caching
- **Monitoring**: Response time metrics

### 4. Race Conditions
- **Risk**: Concurrent requests dengan data sama
- **Mitigation**: Atomic operations + locking
- **Monitoring**: Duplicate detection rate

## Deployment Checklist

### Pre-deployment
- [ ] Database migration tested
- [ ] Feature flags configured
- [ ] Monitoring dashboards ready
- [ ] Rollback procedures documented

### Deployment
- [ ] Deploy dengan feature flags OFF
- [ ] Run database migrations
- [ ] Enable features gradually per service
- [ ] Monitor metrics dan error rates

### Post-deployment
- [ ] Verify idempotency working
- [ ] Check performance impact
- [ ] Monitor duplicate rates
- [ ] Update documentation

## Database Schema Changes

### Required PostgreSQL Queries

Hanya diperlukan perubahan pada **Archive Service** untuk mencegah duplicate storage hasil analisis. Notification Service dan Assessment Service menggunakan in-memory cache (tidak perlu persistent storage).

#### 1. Tambah Kolom Content Hash
```sql
-- Tambah kolom content_hash untuk deduplication
ALTER TABLE archive.analysis_results
ADD COLUMN content_hash VARCHAR(64);
```

#### 2. Buat Index untuk Performance
```sql
-- Index untuk content_hash lookup
CREATE INDEX idx_analysis_results_content_hash
ON archive.analysis_results(content_hash);

-- Index untuk user + content_hash lookup
CREATE INDEX idx_analysis_results_user_content
ON archive.analysis_results(user_id, content_hash);

-- Index untuk deduplication query
CREATE INDEX idx_analysis_results_dedup_lookup
ON archive.analysis_results(user_id, content_hash, assessment_name, status);
```

#### 3. Unique Constraint untuk Mencegah Duplikasi
```sql
-- Unique constraint: satu user tidak bisa punya hasil yang sama untuk assessment yang sama
CREATE UNIQUE INDEX idx_analysis_results_unique_content
ON archive.analysis_results(user_id, content_hash, assessment_name)
WHERE status = 'completed';
```

#### 4. Update Existing Data (Migration)
```sql
-- Function untuk generate content hash (simplified version)
CREATE OR REPLACE FUNCTION generate_content_hash(
    p_user_id UUID,
    p_assessment_data JSONB,
    p_assessment_name VARCHAR
) RETURNS VARCHAR AS $$
DECLARE
    content_string TEXT;
BEGIN
    -- Simplified hash generation (dalam implementasi nyata, gunakan aplikasi)
    content_string := p_user_id::TEXT || p_assessment_name ||
                     COALESCE(p_assessment_data->>'riasec', '') ||
                     COALESCE(p_assessment_data->>'ocean', '') ||
                     COALESCE(p_assessment_data->>'viaIs', '');

    RETURN encode(digest(content_string, 'sha256'), 'hex');
END;
$$ LANGUAGE plpgsql;

-- Update existing records dengan content hash
UPDATE archive.analysis_results
SET content_hash = generate_content_hash(user_id, assessment_data, assessment_name)
WHERE content_hash IS NULL;

-- Drop function setelah migration
DROP FUNCTION generate_content_hash(UUID, JSONB, VARCHAR);
```

#### 5. Verification Queries
```sql
-- Cari potential duplicates berdasarkan content
SELECT
    user_id,
    content_hash,
    assessment_name,
    COUNT(*) as duplicate_count,
    array_agg(id) as result_ids,
    array_agg(created_at) as created_dates
FROM archive.analysis_results
WHERE content_hash IS NOT NULL
GROUP BY user_id, content_hash, assessment_name
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- Statistics untuk duplicate prevention
SELECT
    DATE(created_at) as date,
    COUNT(*) as total_results,
    COUNT(DISTINCT content_hash) as unique_results,
    COUNT(*) - COUNT(DISTINCT content_hash) as prevented_duplicates
FROM archive.analysis_results
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

#### 6. Rollback Queries (Jika Diperlukan)
```sql
-- Rollback: Remove content_hash column dan indexes
DROP INDEX IF EXISTS idx_analysis_results_unique_content;
DROP INDEX IF EXISTS idx_analysis_results_dedup_lookup;
DROP INDEX IF EXISTS idx_analysis_results_user_content;
DROP INDEX IF EXISTS idx_analysis_results_content_hash;
ALTER TABLE archive.analysis_results DROP COLUMN IF EXISTS content_hash;
```

### Urutan Eksekusi Database Changes

1. **Backup database** terlebih dahulu
2. Jalankan query **section 1** (tambah kolom)
3. Jalankan query **section 2** (buat indexes)
4. Jalankan query **section 3** (unique constraint)
5. Jalankan query **section 4** (update existing data)
6. Test dengan query **section 5** (verification)

### Catatan Penting Database

- **Tidak ada persistent storage** untuk Assessment Service dan Notification Service
- **Hanya Archive Service** yang memerlukan perubahan database
- **Content hash** menggunakan SHA-256 dari `userId + assessmentName + assessmentData`
- **Unique constraint** hanya berlaku untuk status 'completed'
- **Migration** untuk existing data menggunakan simplified hash function
- **Rollback** tersedia jika ada masalah
